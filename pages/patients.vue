<script lang="ts">
import {
  PlusIcon,
  UsersIcon,
  MagnifyingGlassIcon,
  ArrowTopRightOnSquareIcon,
  BeakerIcon,
  PencilSquareIcon,
  ArrowPathIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import type { ServerOptions } from "vue3-easy-data-table";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import type { Page, Patient, Header, Request, Response } from "@/types";
import Package from "@/package.json";
import { useAuthStore } from "@/store/auth";

export default {
  components: {
    UsersIcon,
    PlusIcon,
    MagnifyingGlassIcon,
  },
  setup() {
    definePageMeta({
      layout: "dashboard",
    });
    useHead({
      title: `${Package.name.toUpperCase()} - Patients`,
    });
  },
  data() {
    return {
      loading: false as boolean,
      openCreatePatientForm: false as boolean,
      newIcon: BeakerIcon as Object,
      defaultData: {} as {
        firstName: string;
        middleName: string;
        lastName: string;
        gender: string;
      },
      editIcon: PencilSquareIcon as Object,
      viewIcon: ArrowTopRightOnSquareIcon as Object,
      refreshIcon: ArrowPathIcon as Object,
      headers: new Array(
        { text: "patient no", value: "client_id", sortable: true },
        { text: "name", value: "name", sortable: true },
        { text: "sex", value: "sex", sortable: true },
        { text: "date of birth", value: "date_of_birth", sortable: true },
        { text: "physical address", value: "physical_address", sortable: true },
        { text: "actions", value: "actions" }
      ) as Header,
      pages: [
        {
          name: "Home",
          link: "/home",
        },
      ] as Page,
      data: {} as any,
      addIcon: PlusIcon as Object,
      patients: new Array<Patient>(),
      cookie: useCookie("token") as any,
      serverItemsLength: 0,
      serverOptions: <ServerOptions>{
        page: 1,
        rowsPerPage: 25,
        sortBy: "name",
      },
      searchField: "name" as string,
      search: "" as string,
      searchValue: "" as string,
      createPatient: false as boolean,
      showSearch: false as boolean,
    };
  },
  created() {
    this.init();
  },
  methods: {
    /**
     * @method updateSearch get emitted value for search value
     * @param value string as search value
     * @return void
     */
    updateSearch(value: string): void {
      this.searchValue = value;
      this.search = value;
      this.updatePatients(true);
    },
    /***
     * @method init loads patients
     * @param void
     * @returns promise of @type void
     */
    async init(): Promise<void> {
      this.createPatient = false;

      this.defaultData = {
        firstName: "",
        lastName: "",
        middleName: "",
        gender: "",
      };

      this.loading = true;

      const { page, rowsPerPage } = this.serverOptions;
      const authStore = useAuthStore();
      const labLocationId = getIdByName(
        authStore.locations,
        authStore.selectedLocation
      );

      const request: Request = {
        route: `${endpoints.clients}?page=${page}&per_page=${rowsPerPage}&search=${this.search}&lab_location=${labLocationId}`,
        method: "GET",
        token: `${this.cookie}`,
      };

      let { data, error, pending }: Response = await fetchRequest(request);
      this.loading = pending;

      if (data.value) {
        this.patients = data.value.clients;
        this.serverItemsLength = data.value.meta.total_count;
      }

      if (error.value) {
        console.error(error.value.data);
        useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
      }
    },
    /***
     * @method updatePatients reload patients
     * @param value @type any
     * @returns void
     */
    updatePatients(value: any): void {
      if (typeof value === "object") {
        this.serverOptions = value;
      }
      this.init();
    },
    /***
     * @method handleClick navigate to new test
     * @param null
     * @returns void
     */
    async handleClick(patient: Patient): Promise<void> {
      this.data = patient;

      if (patient.source === "remote")
        await this.handleSaveRemotePatient(this.data);
      this.$router.push(`/tests/new-test?patient_id=${patient.client_id}`);
    },
    async handleSaveRemotePatient(details: Patient): Promise<void> {
      const request: Request = {
        route: endpoints.clients,
        method: "POST",
        token: this.cookie,
        body: {
          client: {
            uuid: details.uuid,
          },
          person: {
            first_name: details.first_name,
            middle_name: details.middle_name,
            last_name: details.last_name,
            sex: details.sex,
            date_of_birth: details.date_of_birth,
            birth_date_estimated: false,
          },
          client_identifiers: {
            current_village: details.current_village,
            current_district: details.current_district,
            current_traditional_authority:
              details.current_traditional_authority,
            physical_address: `${details.home_village} ${details.home_traditional_authority} ${details.home_district}`,
            npid: details.npid,
          },
        },
      };

      const { data, error, pending }: Response = await fetchRequest(request);

      if (data.value) this.data = data.value;

      if (error.value) useNuxtApp().$toast.error(`${ERROR_MESSAGE}`);
    },
    handleActionCompleted(data: Record<string, any>): void {
      if (data.patient.length > 0) {
        this.createPatient = true;
        this.patients = data.patient;
        this.serverItemsLength = data.patient.length;
        this.showSearch = true;
      } else {
        useNuxtApp().$toast.warning("Patient not found");
        this.openCreatePatientForm = true;
        this.defaultData = data.defaults;
        this.showSearch = false;
      }
    },
    setCreatePatientFormVisibility(visibility: boolean): void {
      this.openCreatePatientForm = visibility;
    },
  },
  computed: {
    /***
     * @method filteredPatients
     * @param void
     * @returns patients @type Object
     */
    filteredPatients() {
      return this.patients.map((patient) => ({
        ...patient,
        name: capitalize(
          `${patient.first_name} ${patient.middle_name != null ? patient.middle_name : ""
          } ${patient.last_name}`
        ),
        date_of_birth: moment(patient.date_of_birth).format(DATE_FORMAT),
      }));
    },
  },
};
</script>

<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center space-x-2 py-5">
      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
        <path fill="currentColor"
          d="M16 11a4 4 0 1 0 0-8a4 4 0 0 0 0 8m-6-3.5a3.5 3.5 0 1 1-7 0a3.5 3.5 0 0 1 7 0m19 0a3.5 3.5 0 1 1-7 0a3.5 3.5 0 0 1 7 0M9.377 13a3.98 3.98 0 0 0-.877 2.5V23c0 1.235.298 2.4.827 3.427A5 5 0 0 1 2 22v-6.5A2.5 2.5 0 0 1 4.5 13zm13.296 13.427A7.5 7.5 0 0 0 23.5 23v-7.5c0-.946-.328-1.815-.877-2.5H27.5a2.5 2.5 0 0 1 2.5 2.5V22a5 5 0 0 1-7.327 4.427M12.5 13a2.5 2.5 0 0 0-2.5 2.5V23a6 6 0 0 0 12 0v-7.5a2.5 2.5 0 0 0-2.5-2.5z" />
      </svg>
      <h3 class="text-2xl font-semibold uppercase">Patients</h3>
    </div>

    <div class="flex justify-between items-center w-full py-2 mb-2">
      <div class="flex items-center space-x-2">
        <CoreActionButton color="warning" :icon="refreshIcon" text="Refresh" :click="() => {
            init();
          }
          " />
        <CoreActionButton v-show="createPatient" :click="() => {
            openCreatePatientForm = true;
          }
          " text="Create a new patient" color="success" :icon="addIcon" />
      </div>
      <PatientsAddDialog :openForm="openCreatePatientForm" @sethandleDialog="setCreatePatientFormVisibility"
        @on-patient-created="updatePatients" :default-data="defaultData"
        v-if="usePermissions().can.manage('patients')" />

      <PatientsFindDialog @action-completed="handleActionCompleted" v-if="usePermissions().can.manage('patients')" />
    </div>

    <div class="mb-2.5" v-show="showSearch">
      Showing results for
      <span class="text-lg font-medium">{{
        `${defaultData?.firstName} ${defaultData?.lastName}`
        }}</span>
    </div>

    <CoreDatatable :headers="headers" :data="filteredPatients" :loading="loading" :searchField="searchField"
      :searchValue="searchValue" :serverItemsLength="serverItemsLength" :serverOptions="serverOptions"
      @update="updatePatients" v-if="usePermissions().can.manage('patients')">
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <CoreActionButton :click="() => handleClick(item)" text="New Test" color="primary" :icon="newIcon" />

          <PatientsViewDialog :data="item" />
          <PatientsEditDialog :data="item" @update="updatePatients" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>