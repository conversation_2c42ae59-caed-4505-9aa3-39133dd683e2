<template>
  <div class="py-5 px-5">
    <CoreBreadcrumb :pages="pages" />

    <div class="flex items-center justify-between py-5">
      <div class="text-2xl font-semibold flex items-center uppercase">
        <img
          src="@/assets/icons/blood_drop.svg"
          alt="report-icon"
          class="w-8 h-8 mr-2"
        />
        {{ header }}
      </div>
    </div>

    <div class="flex justify-end w-full px-2 py-2 mb-2">
      <CoreSearchBar v-model:search="search" @update="updateSearch" />
    </div>

    <CoreDatatable
      :headers="headers"
      :data="specimens"
      :loading="loading"
      :search-value="searchValue"
      :search-field="'name'"
      v-if="usePermissions().can.manage('test_catalog')"
    >
      <template #item-description="{ item }">
        <div
          class="line-clamp-1"
          :class="item.description ? '' : 'italic text-sm text-zinc-600'"
          v-html="item.description || 'No description available'"
        ></div>
      </template>
      <template v-slot:actions="{ item }">
        <div class="py-2 flex items-center space-x-2">
          <SpecimenTypesViewDialog :data="item" />
          <SpecimenTypesEditDialog :data="item" @update="updateSpecimenTypes" />
        </div>
      </template>
    </CoreDatatable>
  </div>
</template>

<script setup lang="ts">
import type { Header, Page, Request, Response } from "@/types";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import Package from "@/package.json";

definePageMeta({
  layout: "dashboard",
  middleware: ["test-catalog"],
});

useHead({
  title: `${Package.name.toUpperCase()} - Specimen Types`,
});

const header = ref<string>("Specimen Types");
const specimens = ref<Object[]>([]);
const pages = ref<Page>([
  {
    name: "Home",
    link: "/home",
  },
  {
    name: "Test Catalog",
    link: "#",
  },
]);
const loading = ref<boolean>(false);
const search = ref<string>("");
const searchValue = ref<string>("");
const headers = ref<Header>([
  { text: "id", value: "id", sortable: true },
  { text: "name", value: "name", sortable: true },
  { text: "preferred name", value: "preferred_name", sortable: true },
  { text: "nlims code", value: "nlims_code", sortable: true },
  { text: "description", value: "description", sortable: true },
  { text: "actions", value: "actions", sortable: false },
]);

const updateSearch = (value: string): void => {
  searchValue.value = value;
  search.value = value;
};

const init = async (): Promise<void> => {
  loading.value = true;
  const request: Request = {
    route: endpoints.specimens,
    method: "GET",
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending.value;

  if (data.value) {
    specimens.value = data.value;
  }

  if (error.value) {
    console.error(error.value);
  }
};

const updateSpecimenTypes = (value: boolean): void => {
  if (value) {
    init();
  }
};

onMounted(() => {
  init();
});
</script>

<style></style>
