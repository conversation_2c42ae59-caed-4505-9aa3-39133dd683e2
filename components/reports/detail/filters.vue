<template>
  <div>
    <CoreActionButton
      text="Apply filters"
      color="primary"
      :icon="FunnelIcon"
      :click="openFiltersDrawer"
    >
    </CoreActionButton>
    <TransitionRoot as="template" :show="open">
      <Dialog class="relative z-10" @close="open = false">
        <TransitionChild
          as="template"
          enter="ease-in-out duration-500"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in-out duration-500"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div
            class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity"
          />
        </TransitionChild>

        <div class="fixed inset-0 overflow-hidden">
          <div class="absolute inset-0 overflow-hidden">
            <div
              class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10"
            >
              <TransitionChild
                as="template"
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enter-from="translate-x-full"
                enter-to="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leave-from="translate-x-0"
                leave-to="translate-x-full"
              >
                <DialogPanel
                  class="pointer-events-auto relative w-screen max-w-md"
                >
                  <TransitionChild
                    as="template"
                    enter="ease-in-out duration-500"
                    enter-from="opacity-0"
                    enter-to="opacity-100"
                    leave="ease-in-out duration-500"
                    leave-from="opacity-100"
                    leave-to="opacity-0"
                  >
                    <div
                      class="absolute left-0 top-0 -ml-8 flex pr-2 pt-4 sm:-ml-10 sm:pr-4"
                    >
                      <button
                        type="button"
                        class="relative text-gray-100 hover:text-white"
                        @click="open = false"
                      >
                        <span class="absolute -inset-2.5" />
                        <span class="sr-only">Close panel</span>
                        <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                      </button>
                    </div>
                  </TransitionChild>
                  <div
                    class="flex h-full flex-col overflow-y-scroll bg-white py-3 shadow-xl"
                  >
                    <div class="border-b">
                      <DialogTitle
                        class="text-xl font-semibold leading-6 text-gray-900 px-4 py-2"
                        >Filters</DialogTitle
                      >
                    </div>
                    <div class="relative mt-6 flex-1 flex flex-col space-y-3">
                      <div class="px-5 flex flex-col space-y-3">
                        <div>
                          <CoreMultiselect
                            label="Specimen"
                            mode="tags"
                            :items="specimens.map((s) => s.name)"
                            v-model:items-selected="selectedSpecimen"
                          />
                        </div>

                        <div>
                          <CoreMultiselect
                            label="Lab location"
                            mode="tags"
                            :items="locations.map((l) => l.name)"
                            v-model:items-selected="selectedLocation"
                          />
                        </div>

                        <div classs="flex flex-col items-center">
                          <label
                            v-for="field in tableFields"
                            :key="field.label"
                            class="flex items-center pt-3"
                          >
                            <Switch
                              v-model="field.value"
                              :class="
                                field.value ? 'bg-sky-500' : 'bg-gray-200'
                              "
                              class="relative mr-2 inline-flex w-11 h-6 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75"
                            >
                              <span
                                aria-hidden="true"
                                :class="
                                  field.value
                                    ? 'translate-x-5'
                                    : 'translate-x-0'
                                "
                                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out"
                              />
                            </Switch>
                            {{ field.label }}
                          </label>
                        </div>
                      </div>

                      <div
                        class="absolute w-full border-t bottom-0 justify-end flex px-5 pt-3 items-center space-x-5"
                      >
                        <CoreActionButton
                          text="Reset"
                          color="error"
                          :icon="ArchiveBoxXMarkIcon"
                          :click="resetFilters"
                        />
                        <CoreActionButton
                          text="Apply"
                          color="success"
                          :icon="CheckBadgeIcon"
                          :click="applyFilters"
                        />
                      </div>
                    </div>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  ArchiveBoxXMarkIcon,
  CheckBadgeIcon,
  FunnelIcon,
} from "@heroicons/vue/24/solid";
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Switch,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import { XMarkIcon } from "@heroicons/vue/24/outline";
import type { Location, Specimen, TableField } from "@/types";
import fetchRequest from "~/services/fetch";
import { endpoints } from "~/services/endpoints";

const open = ref<boolean>(false);
const { $toast } = useNuxtApp();
const openFiltersDrawer = () => {
  open.value = true;
};
const tableFields = ref<TableField[]>([
  {
    label: "Show person who requested test",
    value: false,
    property: "Requested by",
    table_value: "requested_by",
  },
  {
    label: "Show person who completed the test",
    value: false,
    property: "Completed by",
    table_value: "completed",
  },
  {
    label: "Show test tracking numbers",
    value: false,
    property: "tracking number",
    table_value: "tracking_number",
  },
  {
    label: "Show test result value",
    value: false,
    property: "Test result",
    table_value: "result",
  },
  {
    label: "Show date of test result",
    value: false,
    property: "Result date",
    table_value: "result_date",
  },
]);

const specimens = ref<Specimen[]>([]);
const locations = ref<Location[]>([]);
const selectedSpecimen = ref<string[]>([]);
const selectedLocation = ref<string[]>([]);
const cookie = useCookie("token");
const emits = defineEmits([
  "tableFields",
  "filterFields",
  "specimenFields",
  "locationField",
  "originField",
  'onClearFilters'
]);

const applyFilters = (): void => {
  const selectedTableFields = tableFields.value.filter((field) => field.value);
  emits("tableFields", selectedTableFields);
  emits("specimenFields", selectedSpecimen.value.join(','));
  emits("locationField", selectedLocation.value.join(','));
  open.value = false;
  $toast.success("Filters applied successfully");
};

const resetFilters = (): void => {
  open.value = false;
  tableFields.value = tableFields.value.map((field) => ({
    ...field,
    value: false,
  }));
  selectedSpecimen.value = [];
  selectedLocation.value = [];
  emits('onClearFilters', tableFields.value);
  $toast.success("Filters reset successfully");
};

const getSpecimens = async (): Promise<void> => {
  const { data, error, pending } = await fetchRequest({
    route: endpoints.specimens,
    method: "GET",
    token: `${cookie.value}`,
  });
  if (data) {
    specimens.value = data.value;
  }

  if (error) {
  }
};

const getLocations = async (): Promise<void> => {
  const { data, error, pending } = await fetchRequest({
    route: endpoints.locations,
    method: "GET",
    token: `${cookie.value}`,
  });
  if (data) {
    locations.value = data.value;
  }
};

onMounted(() => {
  getSpecimens();
  getLocations();
});
</script>

<style lang="scss"></style>
