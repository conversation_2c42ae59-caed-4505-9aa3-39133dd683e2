<template>
  <div>
    <div>
      <table class="w-full text-left">
        <thead class="uppercase bg-gray-100">
          <tr class="border-b">
            <th class="uppercase py-2 px-2 border-r">SN</th>
            <th class="uppercase py-2 px-2 border-r">PATIENT NO</th>
            <th class="uppercase py-2 px-2 border-r">PATIENT NAME</th>
            <th class="uppercase py-2 px-2 border-r">AGE</th>
            <th class="uppercase py-2 px-2 border-r">SEX</th>
            <th class="uppercase py-2 px-2 border-r">ACCESSION NUMBER</th>
            <th class="uppercase py-2 px-2 border-r">SPECIMEN TYPE</th>
            <th class="uppercase py-2 px-2 border-r">TESTS</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(report, index) in details" :key="index">
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ index+1 }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ report.patient_no }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ report.patient_name }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ calculateAge(report.dob) }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ report.gender }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ report.accession_number }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b">
              {{ report.specimen }}
            </th>
            <th class="px-2 py-2 font-normal border-r border-b space-y-1">
              <p v-for="(test_type, index) in report.test_type" :key="index">
                {{ test_type }}
              </p>
            </th>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="px-3 py-3 uppercase">SUMMARY</div>
    <table class="w-full text-left">
      <thead class="uppercase bg-gray-100">
        <tr class="border-b">
          <th class="uppercase py-2 px-2 border-r">TOTAL VISITS</th>
          <th class="uppercase py-2 px-2 border-r">MALE</th>
          <th class="uppercase py-2 px-2 border-r">FEMALE</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th class="px-2 py-2 font-normal border-r border-b">
            {{ data.visits.length }}
          </th>
          <th class="px-2 py-2 font-normal border-r border-b">
            {{
              removeDuplicates(details).filter(
                (detail: any) => detail.gender == "M"
              ).length
            }}
          </th>
          <th class="px-2 py-2 font-normal border-r border-b">
            {{
              removeDuplicates(details).filter(
                (detail: any) => detail.gender == "F"
              ).length
            }}
          </th>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
export default defineComponent({
  props: {
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      details: this.data?.data,
    };
  },
  methods: {
    removeDuplicates(reportData: Array<{ patient_no: number }>): Array<Object> {
      const uniquePatients = reportData.filter((patient, index) => {
        return (
          reportData.findIndex(
            (p: any) => p.patient_no === patient.patient_no
          ) === index
        );
      });
      return uniquePatients;
    },
  },
});
</script>
