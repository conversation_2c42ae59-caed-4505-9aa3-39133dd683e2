<template>
    <div class="w-full overflow-x-auto">
        <table class="w-full text-left overflow-y-auto">
            <thead class="uppercase bg-gray-100">
                <tr class="border-b">
                    <th class="uppercase py-2 px-2 border-r" v-for="header, index in headers" :key="index">
                        {{ header.name }}
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr class="bg-white border-b" v-for="report, index in data.data" :key="index">
                    <th class="px-2 py-2 font-normal border-r">
                        {{ index + 1 }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.patient_id }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.patient_name }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.accession_number }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.specimen }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ moment(report.created_date).format(DATE_TIME_FORMAT) }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.test }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.performed_by }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        <p v-for="(result, test) in report.results" :key="test">
                            <span v-if="result !== null && result !== undefined && result !== ''" class="font-medium">{{
                                `${test}:` }}</span> {{ result }}
                        </p>
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.remarks }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.result_date ? moment(report.result_date).format(DATE_TIME_FORMAT) : "" }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.authorized_by }}
                    </th>
                    <th class="px-2 py-2 font-normal border-r">
                        {{ report.authorized_date ? moment(report.authorized_date).format(DATE_TIME_FORMAT) : "" }}
                    </th>
                    <th class="px-2 py-2 font-normal">
                        {{ report.tat }}
                    </th>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script lang="ts">

import moment from 'moment';

export default defineComponent({
    props: {
        data: {
            required: true,
            type: Object
        }
    },
    data() {
        return {
            moment: moment,
            headers: [
                {
                    name: "SN"
                },
                {
                    name: "Patient ID"
                },
                {
                    name: "Patient Name"
                },
                {
                    name: "Accession Number"
                },
                {
                    name: "Specimen"
                },
                {
                    name: "Receipt Date"
                },
                {
                    name: "Tests"
                },
                {
                    name: "Performed By"
                },
                {
                    name: "Results"
                },
                {
                    name: "Remarks"
                },
                {
                    name: "Results Entry Date"
                },
                {
                    name: "Authorized By"
                }, {
                    name: "Authorized Date"
                }, {
                    name: "Turn Around Time"
                }
            ] as Array<{ name: String }>,
            reportData: this.data
        }
    },
})
</script>
