<template>
  <div>
    <Vue3EasyDataTable
      :headers="headers"
      :items="data"
      buttons-pagination
      theme-color="#0ea5e9"
      table-class-name="header"
      alternating
      :loading="loading"
      :search-field="searchField"
      :search-value="searchValue"
      :rows-items="rowsPerItems"
      v-model:server-options="options"
      :sort-by="sortBy"
      :sort-type="sortType"
      :server-items-length="serverItemsLength"
    >
      <template #header="{ text }">
        <p class="uppercase">{{ text }}</p>
      </template>

      <template #item-actions="item">
        <slot name="actions" v-bind:item="item"></slot>
      </template>

      <template v-for="(_, name) in $slots" :key="name" v-slot:[name]="slotData">
        <slot :name="name" v-bind:item="slotData"></slot>
      </template>

      <template #loading>
        <CoreLoader :loading="true" />
      </template>
    </Vue3EasyDataTable>
  </div>
</template>

<script lang="ts">
import type { Item, ServerOptions } from "vue3-easy-data-table";

export default {
  props: {
    headers: {
      type: Array as PropType<any[]>,
      required: true
    },
    data: {
      required: true,
      type: Array as PropType<Item[]>,
    },
    loading: {
      required: false,
      type: Boolean,
      default: false,
    },
    searchField: {
      required: false,
      type: String,
    },
    searchValue: {
      required: false,
      type: String,
    },
    serverOptions: {
      required: false,
      type: Object,
    },
    serverItemsLength: {
      required: false,
      type: Number,
    },
    sortBy: {
      required: false,
      type: String,
    },
    rowsPerItems: {
      required: false,
      type: Array<number>,
      default: [20, 30, 50, 100, 150]
    },
    sortType: {
      required: false,
      type: String,
    },
  },
  data() {
    return {
      options: <Partial<ServerOptions>>this.serverOptions,
      isTestPanel: false as boolean,
    };
  },
  watch: {
    options: {
      handler(newValue, oldValue) {
        this.$emit("update", newValue);
      },
    },
  },
};
</script>

<style scoped>
.header {
  --easy-table-header-background-color: #f9fafb !important;
  --easy-table-footer-background-color: #f9fafb !important;
}
</style>
