<template>
  <div class="col-span-1 rounded border relative">
    <div v-if="!loading">
      <div
        class="flex items-center justify-between bg-gray-50 border-b px-2 py-2 rounded-t"
      >
        <h3 class="text-lg font-semibold">
          Recent Tests <span class="text-base font-medium">({{ getDepartment }})</span>
        </h3>
      </div>
      <div>
        <table class="w-full">
          <tbody>
            <tr
              class="border-b border-dotted cursor-pointer hover:bg-sky-50 transition duration-150"
              v-for="(test, index) in tests"
              :key="index"
              :class="index % 2 !== 0 ? 'bg-gray-50' : ''"
              @click="$router.push(`/tests?search=${test.accession_number}`)"
            >
              <td class="px-2 py-2 capitalize flex items-center">
                <div
                  :style="{ backgroundColor: getColor(test.status as Statuses) }"
                  class="w-3 h-3 rounded-full mr-2"
                ></div>
                {{
                  `${capitalizeStr(test.client.first_name.toLowerCase())}
                    ${
                      test.client.middle_name !== null
                        ? test.client.middle_name
                        : ""
                    } ${capitalizeStr(test.client.last_name.toLowerCase())}`
                }}
              </td>
              <td class="px-2 py-2">{{ String(testDisplayType) === 'full_name' ? test.test_type_name : test.test_type_preferred_name }}</td>
              <td class="px-2 py-2 capitalize">
                {{ test.status.split("-").join(" ") }}
              </td>
            </tr>
          </tbody>
        </table>
        <CoreActionButton
          :click="viewTests"
          text="View tests &rarr;"
          color="primary"
          class="m-2 bottom-0"
        />
      </div>
    </div>
    <div v-if="loading">
      <div class="w-full flex items-center justify-between rounded-t px-2 py-2">
        <div class="h-8 w-48 bg-gray-100 animate-pulse rounded"></div>
        <div class="rounded-full h-7 w-7 bg-gray-100 animate-pulse"></div>
      </div>
      <div class="mt-2 space-y-2 px-2">
        <div class="flex items-center space-x-2" v-for="i in 10" :key="i">
          <div class="h-5 w-5 rounded-full bg-gray-100 animate-pulse"></div>
          <div class="w-full bg-gray-100 h-8 animate-pulse rounded"></div>
        </div>
      </div>
      <div class="w-32 bg-gray-100 rounded-t h-8 animate-pulse m-2"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { EllipsisVerticalIcon } from "@heroicons/vue/20/solid/index.js";
import type { Department, Request, Response, Statuses, Test } from "@/types";
import fetchRequest from "@/services/fetch";
import { endpoints } from "@/services/endpoints";
import { useAuthStore } from "@/store/auth";
import { getStatusColor } from "@/utils/functions";
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { usePreference } from '@/composables/usePreference';

const router = useRouter();
const authStore = useAuthStore();
const cookie = useCookie("token");
const tests = ref<Test[]>([]);
const loading = ref(false);

// Use the preference composable to get test display preference
const { value: testDisplayType } = usePreference("test_name_display", "preferred_name");

const getDepartment = computed((): string => {
  return authStore.department === 'Lab Reception' ? 'All' : authStore.department;
});

const getColor = (status: Statuses): string => {
  return getStatusColor(status);
};

const viewTests = (): void => {
  router.push("/tests");
};

const capitalizeStr = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const getIdByName = (items: { id: number; name: string }[], name: string): number => {
  const item = items.find(item => item.name === name);
  return item ? item.id : 0;
};

const init = async (): Promise<void> => {
  loading.value = true;

  const department = authStore.user.departments.find(
    (department: Department) => department.name === authStore.department
  );

  const department_id = department ? department.id : null;
  const lab_location_id = getIdByName(
    authStore.locations.filter((loc: { id?: number; name: string }) => loc.id !== undefined) as { id: number; name: string }[],
    authStore.selectedLocation
  );

  const request: Request = {
    route: `${endpoints.tests}?minimal=true&page=1&per_page=9&status=&search=&department_id=${department_id}&lab_location=${lab_location_id}&start_date=&end_date=`,
    method: "GET",
    token: `${cookie.value}`,
  };

  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending;

  if (data.value) {
    tests.value = data.value.data;
    loading.value = false;
  }

  if (error.value) {
    console.error("recent-tests: ", error.value);
    loading.value = false;
  }
};

// Watch for changes to authStore
watch(() => [authStore.department, authStore.selectedLocation], () => {
  init();
}, { deep: true });

// Initialize on component mount
onMounted(() => {
  init();
});
</script>

<style scoped></style>
