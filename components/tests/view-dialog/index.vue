<template>
  <div>
    <slot @click="init"></slot>

    <div v-if="details !== null && details.client !== undefined">
      <CorePrinter class="hidden" ref="corePrinter" :orderId="`${details.order_id}`" :id="`${details.client.id}`" />
    </div>

    <TransitionRoot appear :show="show" as="template">
      <Dialog as="div" class="relative z-10">
        <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0" enter-to="opacity-100"
          leave="duration-200 ease-in" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-black bg-opacity-25"></div>
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center text-center">
            <TransitionChild as="template" enter="duration-300 ease-out" enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100" leave="duration-200 ease-in" leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95">
              <DialogPanel
                class="w-full max-w-7xl m-20 transform overflow-hidden rounded bg-white text-left align-middle shadow-xl transition-all">
                <div class="border-b px-3 py-3 flex items-center justify-between">
                  <DialogTitle as="h3" class="text-lg flex items-center font-medium leading-6">
                    <img src="@/assets/icons/prescription_document.svg" class="w-8 h-8 mr-2" />
                    View Test
                  </DialogTitle>

                  <button @click="handleClick">
                    <XMarkIcon class="w-5 h-5" />
                  </button>
                </div>

                <div v-show="loading" class="flex items-center justify-center mx-auto my-20">
                  <CoreLoader :loading="loading" />
                </div>

                <div class="grid grid-cols-3 gap-4 px-5 py-5">
                  <div class="rounded border">
                    <div class="px-2 py-2 bg-gray-50 border-b rounded-t">
                      <h3 class="text-lg font-semibold text-black flex items-center">
                        <img src="@/assets/icons/fever.svg" class="w-7 h-7 mr-1" />
                        Patient
                      </h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Patient Number</h3>
                        <p>{{ details.client.id }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Name</h3>
                        <p>
                          {{
                            capitalize(
                              `${details.client.first_name} ${details.client.middle_name !== null
                                ? details.client.middle_name
                                : ""
                              } ${details.client.last_name}`
                            )
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Sex</h3>
                        <p>{{ details.client.sex }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Age</h3>
                        <p>{{ calculateAge(details.client.date_of_birth) }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="rounded border">
                    <div class="px-2 py-2 bg-gray-50 border-b rounded-t">
                      <h3 class="text-lg font-semibold text-black flex items-center">
                        <img src="@/assets/icons/test_tube.svg" class="w-7 h-7 mr-1" />
                        Specimen
                      </h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Specimen Type</h3>
                        <p>{{ String(testDisplayNamePreference) === "full_name" ? details.specimen_type : details.specimen_preferred_name }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Tracking Number</h3>
                        <p>{{ details.tracking_number }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Accession Number</h3>
                        <p>{{ details.accession_number }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Status</h3>
                        <p>
                          {{
                            capitalize(
                              details.order_status.split("-").join(" ")
                            )
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Turn Around Time</h3>
                        <p>{{ getTurnAroundTime(details) }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="rounded border max-h-72 overflow-y-auto">
                    <div class="px-2 py-2 bg-gray-50 border-b rounded-t flex items-center">
                      <img src="@/assets/icons/i_exam_qualification.svg" class="w-7 h-7 mr-1" />
                      <h3 class="text-lg font-semibold text-black">Test</h3>
                    </div>
                    <div class="w-full space-y-2 py-2">
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Name</h3>
                        <p>{{ String(testDisplayNamePreference) === "full_name" ? details.test_type_name : details.test_type_preferred_name }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Date Registered</h3>
                        <p>
                          {{ moment(details.created_date).format(DATE_FORMAT) }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Receipt Date</h3>
                        <p>
                          {{ moment(details.created_date).format(DATE_FORMAT) }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Test Status</h3>
                        <p>
                          {{
                            capitalizeStr(details.status.split("-").join(" "))
                          }}
                        </p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Ward/Location</h3>
                        <p>{{ details.requesting_ward }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Request Origin</h3>
                        <p>{{ details.request_origin }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-1">
                        <h3 class="font-semibold">Registered By</h3>
                        <p>{{ getRequestedBy(details.status_trail) }}</p>
                      </div>
                      <div class="w-full flex justify-between px-4 py-2 bg-gray-50 border-b border-t border-dotted">
                        <h3 class="font-semibold">Requested By</h3>
                        <p>{{ details.requested_by }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mx-5 rounded border mb-5">
                  <div class="flex items-center justify-between bg-gray-50 px-4 py-2 border-b rounded-t">
                    <h3 class="text-lg font-semibold text-black">Results</h3>
                    <div class="justify-end flex items-center space-x-3">
                      <CoreActionButton :icon="checkIcon" text="Authorize" :click="() => {
                        changeStatus('verified', details.id);
                      }
                        " color="success" :loading="verifying" v-if="
                          details.status.toLowerCase() === 'completed' &&
                          usePermissions().can.verify('test_results') &&
                          isCompletedByCurrentUserOrSuperAdmin(details)
                        " />
                      <CoreActionButton :icon="viewIcon" text="View Report" :click="viewReport" color="primary" v-if="
                        (details.status.toLowerCase() === 'completed' ||
                          details.status.toLowerCase() === 'verified' ||
                          details.status.toLowerCase() === 'rejected' ||
                          details.status.toLowerCase() === 'test-rejected') &&
                        usePermissions().can.view('reports')
                      " />
                      <TestsAddTestOrderDialog :item="details as any" @update="updateChanges" color="primary" />
                      <TestsAddOrderEncounter :item="details as any" @update="updateNewOrder" color="primary" />
                      <CoreActionButton :icon="printerIcon" text="Print Accession Number" :click="printAccessionNumber"
                        color="primary" />
                      <CoreActionButton :icon="printerIcon" color="success" text="Print Tracking Number"
                        :click="printTrackingNumber" />
                    </div>
                  </div>
                  <div v-if="!isRejectedOrVoided(details.status)">
                    <div :class="details.indicators.length !== Number(index) + 1
                      ? 'w-full px-5 py-2 border-b border-dotted flex justify-between items-center'
                      : 'w-full px-5 py-2 flex justify-between items-center'
                      " v-for="(indicator, index) in details.indicators" :key="index">
                      <h3 class="font-medium">{{ String(testDisplayNamePreference) === "full_name" ? indicator.name : indicator.preferred_name }}</h3>
                      <div class="flex items-center space-x-1">
                        <p v-if="indicator.result" v-html="indicator.result.value
                          ? indicator.result.value
                          : 'Not done'
                          "></p>
                        <span class="text-xs" v-if="indicator.result?.value">
                          {{ indicator?.unit }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div v-else class="px-4 py-2 flex items-center">
                    <img src="@/assets/icons/rdt_result_no_test.svg" class="w-6 h-6 mr-2" alt="no-result-svg" />
                    <h3 class="text-lg font-semibold">
                      Test Rejected:
                      <span class="text-base text-gray-600 font-normal">{{
                        details.rejection_reason
                      }}</span>
                    </h3>
                  </div>

                  <div v-if="
                    details.test_type_name == 'Cross-match' &&
                    details.status.toLowerCase() == 'verified'
                  " class="px-2.5 py-2.5 border-t">
                    <CoreActionButton :loading="zebraPrinting" :click="() => {
                      printBBResults();
                    }
                      " text="Print results" color="primary" :icon="printerIcon" />
                  </div>
                  <div v-if="details.test_type_name.toString().toLowerCase().includes('cross')">
                    <div class="flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-t">
                      <h3 class="text-lg font-semibold text-black">
                        Post Cross-match processes
                      </h3>
                    </div>
                    <div>
                      <div class="w-full px-5 py-2 border-b border-dotted flex justify-between items-center">
                        <h3 class="font-medium">Collected by</h3>
                        <p>
                          {{
                            details.post_crossmatch_process
                              ? details.post_crossmatch_process.collected_by
                              : "Not collected"
                          }}
                        </p>
                      </div>
                      <div class="w-full px-5 py-2 border-b border-dotted flex justify-between items-center">
                        <h3 class="font-medium">Collected date</h3>
                        <p>
                          {{
                            details.post_crossmatch_process
                              ? moment(details.post_crossmatch_process.collection_date).format(DATE_FORMAT_TIME)
                              : "Not collected"
                          }}
                        </p>
                      </div>
                      <div class="w-full px-5 py-2 border-b border-dotted flex justify-between items-center">
                        <h3 class="font-medium">Transfusion outcome</h3>
                        <p>
                          {{
                            details.post_crossmatch_process
                              ? details.post_crossmatch_process.transfusion_outcome
                              : "Not done"
                          }}
                        </p>
                      </div>
                      <div class="w-full px-5 py-2 border-b border-dotted flex justify-between items-center">
                        <h3 class="font-medium">Returned reason</h3>
                        <p>
                          {{
                            details.post_crossmatch_process
                              ? details.post_crossmatch_process.returned_reason
                              : "Not returned"
                          }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import {
  TransitionRoot,
  TransitionChild,
  Dialog,
  DialogPanel,
  DialogTitle,
} from "@headlessui/vue";

import {
  XMarkIcon,
  PrinterIcon,
  ArrowTopRightOnSquareIcon,
  CheckBadgeIcon,
} from "@heroicons/vue/24/solid/index.js";
import moment from "moment";
import { endpoints } from "@/services/endpoints";
import fetchRequest from "@/services/fetch";
import PrinterService from "@/services/printer_service";
import type { Test, Request, Response, StatusTrail, Order } from "@/types";
import { useAuthStore } from "@/store/auth";
import { ref, defineProps, defineEmits } from "vue";

const props = defineProps({
  open: {
    type: Boolean,
    required: false,
  },
});

const emit = defineEmits(["update", "order"]);

const { value: testDisplayNamePreference } = usePreference("test_name_display", "preferred_name");

const show = ref(props.open);
const details = ref({} as Test);
const loading = ref<boolean>(false);
const verifying = ref<boolean>(false);
const zebraPrinting = ref<boolean>(false);

const viewIcon = ArrowTopRightOnSquareIcon;
const checkIcon = CheckBadgeIcon;
const printerIcon = PrinterIcon;

const cookie = useCookie("token");
const authStore = useAuthStore();
const nuxtApp = useNuxtApp();

async function init(item: { id: number }): Promise<void> {
  loading.value = true;
  const request: Request = {
    route: `${endpoints.tests}/${item.id}`,
    method: "GET",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  loading.value = pending.value;
  if (data.value) {
    details.value = data.value;
    if (typeof details.value === "object") {
      handleClick();
    }
  }
  if (error.value) {
    loading.value = false;
    console.error(error.value);
    nuxtApp.$toast.error(ERROR_MESSAGE);
  }
}

async function printBBResults(): Promise<void> {
  const request: Request = {
    route: endpoints.printOutZebra,
    method: "POST",
    token: `${cookie.value}`,
    body: {
      order_id: details.value.order_id,
      tests: [details.value.id],
      is_cross_match: true,
    },
  };
  const { data, error, pending }: Response = await fetchRequest(request);
  zebraPrinting.value = pending.value;
  if (data.value) {
    zebraPrinting.value = false;
    const reader = new FileReader();
    reader.onload = () => {
      const url = URL.createObjectURL(data.value);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${Date.now()}.lbl`);
      link.click();
      URL.revokeObjectURL(url);
    };
    reader.readAsText(data.value);
    nuxtApp.$toast.success("Results printed successfully!");
  }
  if (error.value) {
    zebraPrinting.value = false;
    nuxtApp.$toast.error(ERROR_MESSAGE);
    console.error(error.value);
  }
}

/**
 * @method changeStatus change status of current test
 * @param status string
 * @param id number
 * @returns promise @type void
 */
async function changeStatus(status: string, id: number): Promise<void> {
  verifying.value = true;
  const request: Request = {
    route: `${endpoints.testStatus}/${id}/${status}`,
    method: "PUT",
    token: `${cookie.value}`,
  };
  const { data, error, pending }: any = await fetchRequest(request);
  verifying.value = pending.value;
  if (data.value) {
    verifying.value = false;
    nuxtApp.$toast.success(
      `${details.value.test_type_name} test for ${details.value.client.first_name
      } ${details.value.client.middle_name !== null
        ? details.value.client.middle_name
        : ""
      } ${details.value.client.last_name} verified successfully!`
    );
    viewReport(true);
  }
  if (error.value) {
    console.error(error.value);
    verifying.value = false;
  }
}

function updateChanges(): void {
  emit("update", true);
}

function updateNewOrder(order: Order): void {
  emit("order", order);
  handleClick();
}

function handleClick() {
  show.value = !show.value;
}

function printAccessionNumber() {
  return PrinterService.printSpecimenLabel(details.value.accession_number);
}

function printTrackingNumber() {
  return PrinterService.printTrackingNumber(details.value.tracking_number);
}

function viewReport(printer = false) {
  return navigateTo(
    `/reports/daily/patient-report/${details.value.client.id}?order_id=${details.value.order_id}&printer=${printer}`
  );
}

function getRequestedBy(values: Array<any>): string {
  let name = "";
  values.forEach((item) => {
    if (item.status.name === "pending") {
      name = `${item.initiator.first_name} ${item.initiator.last_name}`;
    }
  });
  return capitalize(name);
}

function isRejectedOrVoided(value: string): Boolean {
  const statuses: Set<string> = new Set([
    "voided",
    "test-rejected",
    "rejected",
    "not-done",
  ]);
  return statuses.has(value.toLowerCase());
}

function isCompletedByCurrentUserOrSuperAdmin(item: {
  status?: string;
  completed_by?: any;
}): boolean {
  const currentUser = authStore.user;
  const completedBy = item.completed_by;
  if (completedBy) {
    if (completedBy.id !== currentUser.id) {
      return true;
    } else if (completedBy.is_super_admin === true) {
      return true;
    }
  }
  return false;
}

function getTurnAroundTime(test: Test): string {
  const testDate = new Date(test.created_date);
  const verifiedTrail = test.status_trail.find(
    (trail: StatusTrail) => trail.status.name.toLowerCase() === "verified"
  );

  if (!verifiedTrail) {
    return "In progress...";
  }

  const verifiedDate = new Date(verifiedTrail.created_date);
  const diff = verifiedDate.getTime() - testDate.getTime();

  const diffInSeconds = Math.floor(diff / 1000);
  const days = Math.floor(diffInSeconds / (3600 * 24));
  const hours = Math.floor((diffInSeconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((diffInSeconds % 3600) / 60);
  const seconds = diffInSeconds % 60;

  const timeParts: string[] = [];
  if (days > 0) timeParts.push(`${days} day${days > 1 ? "s" : ""}`);
  if (hours > 0) timeParts.push(`${hours} hour${hours > 1 ? "s" : ""}`);
  if (minutes > 0) timeParts.push(`${minutes} minute${minutes > 1 ? "s" : ""}`);
  if (seconds > 0) timeParts.push(`${seconds} second${seconds > 1 ? "s" : ""}`);

  return timeParts.length > 0 ? timeParts.join(", ") : "0 seconds";
}

defineExpose({
  init,
  printBBResults,
  changeStatus,
  updateChanges,
  updateNewOrder,
  handleClick,
  printAccessionNumber,
  printTrackingNumber,
  viewReport,
  getRequestedBy,
  isRejectedOrVoided,
  isCompletedByCurrentUserOrSuperAdmin,
  getTurnAroundTime,
});
</script>

<style></style>
